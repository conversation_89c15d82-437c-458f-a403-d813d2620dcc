package com.swcares.service.bkc.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.swcares.core.exception.SguiResultException;
import com.swcares.entity.*;
import com.swcares.obj.dto.RemoveNameDto;
import com.swcares.obj.vo.RemoveNameVo;
import com.swcares.service.*;
import com.swcares.service.bkc.IRemoveNameService;
import com.swcares.service.bkc.IUpdatePnrService;
import com.swcares.service.ISguiCommonService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 删除旅客服务实现类
 *
 * <AUTHOR>
 * @date 2025/1/15 10:30
 */
@Slf4j
@Service
public class RemoveNameServiceImpl implements IRemoveNameService {

    @Resource
    private IMnjxPnrService iMnjxPnrService;

    @Resource
    private IMnjxPnrNmService iMnjxPnrNmService;

    @Resource
    private IMnjxNmXnService iMnjxNmXnService;

    @Resource
    private IMnjxNmSsrService iMnjxNmSsrService;

    @Resource
    private IMnjxNmCtService iMnjxNmCtService;

    @Resource
    private IMnjxNmOsiService iMnjxNmOsiService;

    @Resource
    private IMnjxNmRmkService iMnjxNmRmkService;

    @Resource
    private IMnjxNmFpService iMnjxNmFpService;

    @Resource
    private IMnjxNmFcService iMnjxNmFcService;

    @Resource
    private IMnjxNmFnService iMnjxNmFnService;

    @Resource
    private IMnjxNmEiService iMnjxNmEiService;

    @Resource
    private IMnjxPnrFpService iMnjxPnrFpService;

    @Resource
    private IMnjxPnrFcService iMnjxPnrFcService;

    @Resource
    private IMnjxPnrFnService iMnjxPnrFnService;

    @Resource
    private IMnjxPnrRecordService iMnjxPnrRecordService;

    @Resource
    private IMnjxPnrAtService iMnjxPnrAtService;

    @Resource
    private ISguiCommonService iSguiCommonService;

    @Resource
    private IUpdatePnrService iUpdatePnrService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public RemoveNameVo removeName(RemoveNameDto dto) throws SguiResultException {
        // 1. 根据PNR查询PNR信息
        MnjxPnr pnr = this.getPnrByPnrNo(dto.getPnr());

        // 2. 验证删除条件
        this.validateRemoveConditions(pnr, dto);

        // 3. 收集要删除的数据
        List<Object> deleteList = new ArrayList<>();
        List<Object> updateList = new ArrayList<>();

        // 4. 处理旅客删除
        if (CollUtil.isNotEmpty(dto.getTravellers())) {
            this.processTravellerRemoval(pnr, dto.getTravellers(), deleteList, updateList);
        }

        // 5. 处理婴儿删除
        if (CollUtil.isNotEmpty(dto.getInfants())) {
            this.processInfantRemoval(pnr, dto.getInfants(), deleteList, updateList);
        }

        // 6. 处理PNR级别运价删除
        this.processPnrLevelFareRemoval(pnr, deleteList);

        // 7. 生成新的封口编号并设置changeAtNo
        String newAtNo = iSguiCommonService.generateNewAtNo(pnr.getPnrId());
        this.setChangeAtNoForUpdates(updateList, newAtNo);

        // 8. 批量执行删除和更新操作
        this.batchExecuteOperations(deleteList, updateList);

        // 9. 重新排序和生成封口记录
        this.reorderAndSeal(pnr, dto.getEnvelopType(), newAtNo);

        return new RemoveNameVo();
    }

    /**
     * 根据PNR编号查询PNR信息
     */
    private MnjxPnr getPnrByPnrNo(String pnrNo) throws SguiResultException {
        if (StrUtil.isEmpty(pnrNo)) {
            throw new SguiResultException("PNR编号不能为空");
        }

        MnjxPnr pnr = iMnjxPnrService.lambdaQuery()
                .eq(MnjxPnr::getPnrCrs, pnrNo)
                .one();

        if (pnr == null) {
            throw new SguiResultException("未找到PNR信息：" + pnrNo);
        }

        return pnr;
    }

    /**
     * 验证删除条件
     */
    private void validateRemoveConditions(MnjxPnr pnr, RemoveNameDto dto) throws SguiResultException {
        // 查询所有非婴儿旅客
        List<MnjxPnrNm> allPassengers = iMnjxPnrNmService.lambdaQuery()
                .eq(MnjxPnrNm::getPnrId, pnr.getPnrId())
                .ne(MnjxPnrNm::getPsgType, "3") // 排除婴儿
                .list();

        if (CollUtil.isEmpty(allPassengers)) {
            throw new SguiResultException("PNR中没有旅客信息");
        }

        // 如果要删除旅客，检查是否删除最后一个非婴儿旅客
        if (CollUtil.isNotEmpty(dto.getTravellers())) {
            List<Integer> toDeletePaxIds = dto.getTravellers().stream()
                    .map(RemoveNameDto.Traveller::getPaxId)
                    .collect(Collectors.toList());

            long remainingPassengers = allPassengers.stream()
                    .filter(p -> !toDeletePaxIds.contains(p.getPsgIndex()))
                    .count();

            if (remainingPassengers == 0) {
                throw new SguiResultException("不能删除PNR中的最后一个非婴儿旅客");
            }
        }
    }

    /**
     * 处理旅客删除
     */
    private void processTravellerRemoval(MnjxPnr pnr, List<RemoveNameDto.Traveller> travellers,
                                       List<Object> deleteList, List<Object> updateList) throws SguiResultException {
        for (RemoveNameDto.Traveller traveller : travellers) {
            // 查询旅客信息
            MnjxPnrNm pnrNm = iMnjxPnrNmService.lambdaQuery()
                    .eq(MnjxPnrNm::getPnrId, pnr.getPnrId())
                    .eq(MnjxPnrNm::getPsgIndex, traveller.getPaxId())
                    .one();

            if (pnrNm == null) {
                log.warn("未找到旅客信息，psgIndex: {}", traveller.getPaxId());
                continue;
            }

            // 删除旅客主记录
            deleteList.add(pnrNm);

            // 删除旅客相关的所有数据
            this.deletePassengerRelatedData(pnrNm.getPnrNmId(), deleteList, updateList);
        }
    }

    /**
     * 删除旅客相关数据
     */
    private void deletePassengerRelatedData(String pnrNmId, List<Object> deleteList, List<Object> updateList) {
        // 删除SSR记录（排除婴儿相关的SSR INFT）
        List<MnjxNmSsr> ssrList = iMnjxNmSsrService.lambdaQuery()
                .eq(MnjxNmSsr::getPnrNmId, pnrNmId)
                .ne(MnjxNmSsr::getSsrType, "INFT")
                .list();
        deleteList.addAll(ssrList);

        // 删除CT记录
        List<MnjxNmCt> ctList = iMnjxNmCtService.lambdaQuery()
                .eq(MnjxNmCt::getPnrNmId, pnrNmId)
                .list();
        deleteList.addAll(ctList);

        // 删除OSI记录
        List<MnjxNmOsi> osiList = iMnjxNmOsiService.lambdaQuery()
                .eq(MnjxNmOsi::getPnrNmId, pnrNmId)
                .list();
        deleteList.addAll(osiList);

        // 删除RMK记录
        List<MnjxNmRmk> rmkList = iMnjxNmRmkService.lambdaQuery()
                .eq(MnjxNmRmk::getPnrNmId, pnrNmId)
                .list();
        deleteList.addAll(rmkList);

        // 删除运价记录（排除婴儿运价）
        List<MnjxNmFp> fpList = iMnjxNmFpService.lambdaQuery()
                .eq(MnjxNmFp::getPnrNmId, pnrNmId)
                .ne(MnjxNmFp::getIsBaby, 1)
                .list();
        deleteList.addAll(fpList);

        List<MnjxNmFc> fcList = iMnjxNmFcService.lambdaQuery()
                .eq(MnjxNmFc::getPnrNmId, pnrNmId)
                .ne(MnjxNmFc::getIsBaby, 1)
                .list();
        deleteList.addAll(fcList);

        List<MnjxNmFn> fnList = iMnjxNmFnService.lambdaQuery()
                .eq(MnjxNmFn::getPnrNmId, pnrNmId)
                .ne(MnjxNmFn::getIsBaby, 1)
                .list();
        deleteList.addAll(fnList);

        // 删除EI记录
        List<MnjxNmEi> eiList = iMnjxNmEiService.lambdaQuery()
                .eq(MnjxNmEi::getPnrNmId, pnrNmId)
                .list();
        deleteList.addAll(eiList);

        // 更新相关的PNR记录，设置changeMark为X
        this.updateRelatedPnrRecords(deleteList, updateList);
    }

    /**
     * 处理婴儿删除
     */
    private void processInfantRemoval(MnjxPnr pnr, List<RemoveNameDto.Infant> infants,
                                    List<Object> deleteList, List<Object> updateList) throws SguiResultException {
        for (RemoveNameDto.Infant infant : infants) {
            // 解析成人旅客索引
            String adultIndexStr = infant.getAdaultIndex();
            if (StrUtil.isEmpty(adultIndexStr) || !adultIndexStr.startsWith("P")) {
                log.warn("无效的成人旅客索引: {}", adultIndexStr);
                continue;
            }

            Integer adultPsgIndex = Integer.parseInt(adultIndexStr.substring(1));

            // 查询成人旅客信息
            MnjxPnrNm adultPnrNm = iMnjxPnrNmService.lambdaQuery()
                    .eq(MnjxPnrNm::getPnrId, pnr.getPnrId())
                    .eq(MnjxPnrNm::getPsgIndex, adultPsgIndex)
                    .one();

            if (adultPnrNm == null) {
                log.warn("未找到成人旅客信息，psgIndex: {}", adultPsgIndex);
                continue;
            }

            // 查询婴儿信息
            MnjxNmXn nmXn = iMnjxNmXnService.lambdaQuery()
                    .eq(MnjxNmXn::getPnrNmId, adultPnrNm.getPnrNmId())
                    .one();

            if (nmXn == null) {
                log.warn("未找到婴儿信息，成人pnrNmId: {}", adultPnrNm.getPnrNmId());
                continue;
            }

            // 删除婴儿记录
            deleteList.add(nmXn);

            // 删除婴儿相关数据
            this.deleteInfantRelatedData(adultPnrNm.getPnrNmId(), deleteList, updateList);
        }
    }

    /**
     * 删除婴儿相关数据
     */
    private void deleteInfantRelatedData(String pnrNmId, List<Object> deleteList, List<Object> updateList) {
        // 删除SSR INFT记录
        List<MnjxNmSsr> infantSsrList = iMnjxNmSsrService.lambdaQuery()
                .eq(MnjxNmSsr::getPnrNmId, pnrNmId)
                .eq(MnjxNmSsr::getSsrType, "INFT")
                .list();
        deleteList.addAll(infantSsrList);

        // 删除婴儿运价记录
        List<MnjxNmFp> infantFpList = iMnjxNmFpService.lambdaQuery()
                .eq(MnjxNmFp::getPnrNmId, pnrNmId)
                .eq(MnjxNmFp::getIsBaby, 1)
                .list();
        deleteList.addAll(infantFpList);

        List<MnjxNmFc> infantFcList = iMnjxNmFcService.lambdaQuery()
                .eq(MnjxNmFc::getPnrNmId, pnrNmId)
                .eq(MnjxNmFc::getIsBaby, 1)
                .list();
        deleteList.addAll(infantFcList);

        List<MnjxNmFn> infantFnList = iMnjxNmFnService.lambdaQuery()
                .eq(MnjxNmFn::getPnrNmId, pnrNmId)
                .eq(MnjxNmFn::getIsBaby, 1)
                .list();
        deleteList.addAll(infantFnList);

        // 删除婴儿EI记录
        List<MnjxNmEi> infantEiList = iMnjxNmEiService.lambdaQuery()
                .eq(MnjxNmEi::getPnrNmId, pnrNmId)
                .like(MnjxNmEi::getEiInfo, "IN/")
                .list();
        deleteList.addAll(infantEiList);
    }

    /**
     * 处理PNR级别运价删除
     */
    private void processPnrLevelFareRemoval(MnjxPnr pnr, List<Object> deleteList) {
        // 检查是否需要删除PNR级别的婴儿运价
        this.checkAndRemovePnrInfantFares(pnr, deleteList);

        // 检查是否需要删除PNR级别的儿童运价
        this.checkAndRemovePnrChildFares(pnr, deleteList);

        // 检查是否需要删除PNR级别的成人运价
        this.checkAndRemovePnrAdultFares(pnr, deleteList);
    }

    /**
     * 检查并删除PNR级别的婴儿运价
     */
    private void checkAndRemovePnrInfantFares(MnjxPnr pnr, List<Object> deleteList) {
        // 查询所有成人旅客的ID
        List<String> adultPnrNmIds = iMnjxPnrNmService.lambdaQuery()
                .eq(MnjxPnrNm::getPnrId, pnr.getPnrId())
                .ne(MnjxPnrNm::getPsgType, "3") // 排除婴儿
                .list()
                .stream()
                .map(MnjxPnrNm::getPnrNmId)
                .collect(Collectors.toList());

        if (CollUtil.isEmpty(adultPnrNmIds)) {
            return;
        }

        // 检查是否还有婴儿
        long remainingInfants = iMnjxNmXnService.lambdaQuery()
                .in(MnjxNmXn::getPnrNmId, adultPnrNmIds)
                .count();

        if (remainingInfants == 0) {
            // 删除PNR级别的婴儿运价
            List<MnjxPnrFp> pnrInfantFpList = iMnjxPnrFpService.lambdaQuery()
                    .eq(MnjxPnrFp::getPnrId, pnr.getPnrId())
                    .eq(MnjxPnrFp::getIsBaby, 1)
                    .list();
            deleteList.addAll(pnrInfantFpList);

            List<MnjxPnrFc> pnrInfantFcList = iMnjxPnrFcService.lambdaQuery()
                    .eq(MnjxPnrFc::getPnrId, pnr.getPnrId())
                    .eq(MnjxPnrFc::getIsBaby, 1)
                    .list();
            deleteList.addAll(pnrInfantFcList);

            List<MnjxPnrFn> pnrInfantFnList = iMnjxPnrFnService.lambdaQuery()
                    .eq(MnjxPnrFn::getPnrId, pnr.getPnrId())
                    .eq(MnjxPnrFn::getIsBaby, 1)
                    .list();
            deleteList.addAll(pnrInfantFnList);
        }
    }

    /**
     * 检查并删除PNR级别的儿童运价
     */
    private void checkAndRemovePnrChildFares(MnjxPnr pnr, List<Object> deleteList) {
        // 检查是否还有儿童旅客
        long remainingChildren = iMnjxPnrNmService.lambdaQuery()
                .eq(MnjxPnrNm::getPnrId, pnr.getPnrId())
                .eq(MnjxPnrNm::getPsgType, "1") // 儿童
                .count();

        if (remainingChildren == 0) {
            // 删除PNR级别的儿童运价
            List<MnjxPnrFp> pnrChildFpList = iMnjxPnrFpService.lambdaQuery()
                    .eq(MnjxPnrFp::getPnrId, pnr.getPnrId())
                    .eq(MnjxPnrFp::getPatType, "CH")
                    .list();
            deleteList.addAll(pnrChildFpList);

            List<MnjxPnrFc> pnrChildFcList = iMnjxPnrFcService.lambdaQuery()
                    .eq(MnjxPnrFc::getPnrId, pnr.getPnrId())
                    .eq(MnjxPnrFc::getPatType, "CH")
                    .list();
            deleteList.addAll(pnrChildFcList);

            List<MnjxPnrFn> pnrChildFnList = iMnjxPnrFnService.lambdaQuery()
                    .eq(MnjxPnrFn::getPnrId, pnr.getPnrId())
                    .eq(MnjxPnrFn::getPatType, "CH")
                    .list();
            deleteList.addAll(pnrChildFnList);
        }
    }

    /**
     * 检查并删除PNR级别的成人运价
     */
    private void checkAndRemovePnrAdultFares(MnjxPnr pnr, List<Object> deleteList) {
        // 检查是否还有成人旅客
        long remainingAdults = iMnjxPnrNmService.lambdaQuery()
                .eq(MnjxPnrNm::getPnrId, pnr.getPnrId())
                .eq(MnjxPnrNm::getPsgType, "0") // 成人
                .count();

        if (remainingAdults == 0) {
            // 删除PNR级别的成人运价
            List<MnjxPnrFp> pnrAdultFpList = iMnjxPnrFpService.lambdaQuery()
                    .eq(MnjxPnrFp::getPnrId, pnr.getPnrId())
                    .eq(MnjxPnrFp::getPatType, "AD")
                    .list();
            deleteList.addAll(pnrAdultFpList);

            List<MnjxPnrFc> pnrAdultFcList = iMnjxPnrFcService.lambdaQuery()
                    .eq(MnjxPnrFc::getPnrId, pnr.getPnrId())
                    .eq(MnjxPnrFc::getPatType, "AD")
                    .list();
            deleteList.addAll(pnrAdultFcList);

            List<MnjxPnrFn> pnrAdultFnList = iMnjxPnrFnService.lambdaQuery()
                    .eq(MnjxPnrFn::getPnrId, pnr.getPnrId())
                    .eq(MnjxPnrFn::getPatType, "AD")
                    .list();
            deleteList.addAll(pnrAdultFnList);
        }
    }

    /**
     * 更新相关的PNR记录
     */
    private void updateRelatedPnrRecords(List<Object> deleteList, List<Object> updateList) {
        for (Object deleteItem : deleteList) {
            // 根据删除项查询对应的PNR记录
            List<MnjxPnrRecord> relatedRecords = this.findRelatedPnrRecords(deleteItem);
            for (MnjxPnrRecord record : relatedRecords) {
                record.setChangeMark("X");
                updateList.add(record);
            }
        }
    }

    /**
     * 查找相关的PNR记录
     */
    private List<MnjxPnrRecord> findRelatedPnrRecords(Object deleteItem) {
        List<MnjxPnrRecord> records = new ArrayList<>();

        if (deleteItem instanceof MnjxPnrNm) {
            MnjxPnrNm pnrNm = (MnjxPnrNm) deleteItem;
            // 查询姓名相关的记录
            records.addAll(iMnjxPnrRecordService.lambdaQuery()
                    .eq(MnjxPnrRecord::getPnrId, pnrNm.getPnrId())
                    .eq(MnjxPnrRecord::getPnrType, "NM")
                    .eq(MnjxPnrRecord::getPnrIndex, pnrNm.getPnrIndex())
                    .list());
        } else if (deleteItem instanceof MnjxNmXn) {
            MnjxNmXn nmXn = (MnjxNmXn) deleteItem;
            // 查询婴儿相关的记录
            records.addAll(iMnjxPnrRecordService.lambdaQuery()
                    .eq(MnjxPnrRecord::getPnrIndex, nmXn.getPnrIndex())
                    .like(MnjxPnrRecord::getInputValue, nmXn.getXnCname())
                    .list());
        } else if (deleteItem instanceof MnjxNmSsr) {
            MnjxNmSsr nmSsr = (MnjxNmSsr) deleteItem;
            // 查询SSR相关的记录
            records.addAll(iMnjxPnrRecordService.lambdaQuery()
                    .eq(MnjxPnrRecord::getPnrIndex, nmSsr.getPnrIndex())
                    .eq(MnjxPnrRecord::getPnrType, "SSR")
                    .list());
        }
        // 可以根据需要添加更多类型的处理

        return records;
    }

    /**
     * 设置更新项的changeAtNo
     */
    private void setChangeAtNoForUpdates(List<Object> updateList, String newAtNo) {
        updateList.stream()
                .filter(u -> u instanceof MnjxPnrRecord)
                .map(u -> (MnjxPnrRecord) u)
                .filter(r -> StrUtil.isNotEmpty(r.getChangeMark()))
                .forEach(r -> r.setChangeAtNo(newAtNo));
    }

    /**
     * 批量执行删除和更新操作
     */
    private void batchExecuteOperations(List<Object> deleteList, List<Object> updateList) {
        // 批量删除
        if (CollUtil.isNotEmpty(deleteList)) {
            this.batchDelete(deleteList);
        }

        // 批量更新
        if (CollUtil.isNotEmpty(updateList)) {
            this.batchUpdate(updateList);
        }
    }

    /**
     * 批量删除
     */
    private void batchDelete(List<Object> deleteList) {
        // 按类型分组删除
        List<MnjxPnrNm> pnrNmList = new ArrayList<>();
        List<MnjxNmXn> nmXnList = new ArrayList<>();
        List<MnjxNmSsr> nmSsrList = new ArrayList<>();
        List<MnjxNmCt> nmCtList = new ArrayList<>();
        List<MnjxNmOsi> nmOsiList = new ArrayList<>();
        List<MnjxNmRmk> nmRmkList = new ArrayList<>();
        List<MnjxNmFp> nmFpList = new ArrayList<>();
        List<MnjxNmFc> nmFcList = new ArrayList<>();
        List<MnjxNmFn> nmFnList = new ArrayList<>();
        List<MnjxNmEi> nmEiList = new ArrayList<>();
        List<MnjxPnrFp> pnrFpList = new ArrayList<>();
        List<MnjxPnrFc> pnrFcList = new ArrayList<>();
        List<MnjxPnrFn> pnrFnList = new ArrayList<>();

        // 分类收集
        for (Object item : deleteList) {
            if (item instanceof MnjxPnrNm) {
                pnrNmList.add((MnjxPnrNm) item);
            } else if (item instanceof MnjxNmXn) {
                nmXnList.add((MnjxNmXn) item);
            } else if (item instanceof MnjxNmSsr) {
                nmSsrList.add((MnjxNmSsr) item);
            } else if (item instanceof MnjxNmCt) {
                nmCtList.add((MnjxNmCt) item);
            } else if (item instanceof MnjxNmOsi) {
                nmOsiList.add((MnjxNmOsi) item);
            } else if (item instanceof MnjxNmRmk) {
                nmRmkList.add((MnjxNmRmk) item);
            } else if (item instanceof MnjxNmFp) {
                nmFpList.add((MnjxNmFp) item);
            } else if (item instanceof MnjxNmFc) {
                nmFcList.add((MnjxNmFc) item);
            } else if (item instanceof MnjxNmFn) {
                nmFnList.add((MnjxNmFn) item);
            } else if (item instanceof MnjxNmEi) {
                nmEiList.add((MnjxNmEi) item);
            } else if (item instanceof MnjxPnrFp) {
                pnrFpList.add((MnjxPnrFp) item);
            } else if (item instanceof MnjxPnrFc) {
                pnrFcList.add((MnjxPnrFc) item);
            } else if (item instanceof MnjxPnrFn) {
                pnrFnList.add((MnjxPnrFn) item);
            }
        }

        // 执行批量删除
        if (CollUtil.isNotEmpty(pnrNmList)) {
            iMnjxPnrNmService.removeBatchByIds(pnrNmList.stream().map(MnjxPnrNm::getPnrNmId).collect(Collectors.toList()));
        }
        if (CollUtil.isNotEmpty(nmXnList)) {
            iMnjxNmXnService.removeBatchByIds(nmXnList.stream().map(MnjxNmXn::getNmXnId).collect(Collectors.toList()));
        }
        if (CollUtil.isNotEmpty(nmSsrList)) {
            iMnjxNmSsrService.removeBatchByIds(nmSsrList.stream().map(MnjxNmSsr::getNmSsrId).collect(Collectors.toList()));
        }
        if (CollUtil.isNotEmpty(nmCtList)) {
            iMnjxNmCtService.removeBatchByIds(nmCtList.stream().map(MnjxNmCt::getPnrCtId).collect(Collectors.toList()));
        }
        if (CollUtil.isNotEmpty(nmOsiList)) {
            iMnjxNmOsiService.removeBatchByIds(nmOsiList.stream().map(MnjxNmOsi::getPnrOsiId).collect(Collectors.toList()));
        }
        if (CollUtil.isNotEmpty(nmRmkList)) {
            iMnjxNmRmkService.removeBatchByIds(nmRmkList.stream().map(MnjxNmRmk::getNmRmkId).collect(Collectors.toList()));
        }
        if (CollUtil.isNotEmpty(nmFpList)) {
            iMnjxNmFpService.removeBatchByIds(nmFpList.stream().map(MnjxNmFp::getNmFpId).collect(Collectors.toList()));
        }
        if (CollUtil.isNotEmpty(nmFcList)) {
            iMnjxNmFcService.removeBatchByIds(nmFcList.stream().map(MnjxNmFc::getNmFcId).collect(Collectors.toList()));
        }
        if (CollUtil.isNotEmpty(nmFnList)) {
            iMnjxNmFnService.removeBatchByIds(nmFnList.stream().map(MnjxNmFn::getNmFnId).collect(Collectors.toList()));
        }
        if (CollUtil.isNotEmpty(nmEiList)) {
            iMnjxNmEiService.removeBatchByIds(nmEiList.stream().map(MnjxNmEi::getNmEiId).collect(Collectors.toList()));
        }
        if (CollUtil.isNotEmpty(pnrFpList)) {
            iMnjxPnrFpService.removeBatchByIds(pnrFpList.stream().map(MnjxPnrFp::getPnrFpId).collect(Collectors.toList()));
        }
        if (CollUtil.isNotEmpty(pnrFcList)) {
            iMnjxPnrFcService.removeBatchByIds(pnrFcList.stream().map(MnjxPnrFc::getPnrFcId).collect(Collectors.toList()));
        }
        if (CollUtil.isNotEmpty(pnrFnList)) {
            iMnjxPnrFnService.removeBatchByIds(pnrFnList.stream().map(MnjxPnrFn::getPnrFnId).collect(Collectors.toList()));
        }

        log.info("批量删除完成，共删除 {} 条记录", deleteList.size());
    }

    /**
     * 批量更新
     */
    private void batchUpdate(List<Object> updateList) {
        List<MnjxPnrRecord> pnrRecordList = updateList.stream()
                .filter(u -> u instanceof MnjxPnrRecord)
                .map(u -> (MnjxPnrRecord) u)
                .collect(Collectors.toList());

        if (CollUtil.isNotEmpty(pnrRecordList)) {
            iMnjxPnrRecordService.updateBatchById(pnrRecordList);
            log.info("批量更新完成，共更新 {} 条PNR记录", pnrRecordList.size());
        }
    }

    /**
     * 重新排序和生成封口记录
     */
    private void reorderAndSeal(MnjxPnr pnr, String envelopType, String newAtNo) throws SguiResultException {
        // 重新排序所有项的pnr_index
        List<MnjxPnrRecord> recordList = new ArrayList<>();
        iUpdatePnrService.reorderAllPnrIndexesAndUpdate(pnr, recordList);

        // 生成封口记录
        this.generateSealingRecord(pnr, recordList, envelopType, newAtNo);
    }

    /**
     * 生成封口记录
     */
    private void generateSealingRecord(MnjxPnr pnr, List<MnjxPnrRecord> recordList, String envelopType, String newAtNo) throws SguiResultException {
        // 创建封口记录
        MnjxPnrAt pnrAt = new MnjxPnrAt();
        pnrAt.setPnrAtId(IdUtil.getSnowflake(1, 1).nextIdStr());
        pnrAt.setPnrId(pnr.getPnrId());
        pnrAt.setAtNo(newAtNo);
        pnrAt.setAtDateTime(new Date());

        // 设置封口类型
        if (StrUtil.isNotEmpty(envelopType)) {
            if ("I".equals(envelopType)) {
                pnrAt.setAtType("I");
            } else if ("KI".equals(envelopType)) {
                pnrAt.setAtType("KI");
            }
        }

        // 获取当前用户信息并设置
        try {
            pnrAt.setAtSiId(iSguiCommonService.getCurrentUserInfo().getSiId());
        } catch (Exception e) {
            log.warn("获取当前用户信息失败", e);
        }

        // 保存封口记录
        iMnjxPnrAtService.save(pnrAt);
    }
}
