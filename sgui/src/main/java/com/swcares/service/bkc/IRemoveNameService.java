package com.swcares.service.bkc;

import com.swcares.core.exception.SguiResultException;
import com.swcares.obj.dto.RemoveNameDto;
import com.swcares.obj.vo.RemoveNameVo;

/**
 * 删除旅客服务接口
 *
 * <AUTHOR>
 * @date 2025/1/15 10:30
 */
public interface IRemoveNameService {

    /**
     * 删除旅客
     *
     * @param dto 删除旅客请求参数
     * @return 删除结果
     * @throws SguiResultException 异常
     */
    RemoveNameVo removeName(RemoveNameDto dto) throws SguiResultException;
}
